default:
    tags:
        - linux/amd64
stages:
   - build

build:
    stage: build
    image: registry.qiuzhi.tech/mirror/library/docker:27.0.3
    variables:
        DOCKER_HOST: tcp://dockerhost:2375
        DOCKER_TLS_CERTDIR: ""
        DOCKER_BUILDKIT: '1'
        NPROC: 48
    services:
        - name: registry.qiuzhi.tech/mirror/library/docker:27.0.3-dind-rootless
          alias: dockerhost
          entrypoint: ["dockerd-entrypoint.sh", "--tls=false"]
    before_script:
        - docker info
        - docker login -u $REGISTRY_USERNAME -p $REGISTRY_SECRET  $REGISTRY_URL
    script:
        - SANITIZED_BRANCH=$(echo "${CI_COMMIT_BRANCH}" | sed 's|/|-|g')
        - docker build . -t $REGISTRY_URL/devops/dataserver-train:${SANITIZED_BRANCH}
        - docker push $REGISTRY_URL/devops/dataserver-train:${SANITIZED_BRANCH}
#    rules:
#      - if: $CI_COMMIT_BRANCH == "develop"