from flask import Flask
from models import db
from datetime import timedelta
from config import MYSQL_USER,MYSQL_DB,MYSQL_HOST,MYSQL_PASSWORD
from api.TrainInterface.TrainDataApi import TrainDataUrls


def create_app():
    app = Flask(__name__)
    app.register_blueprint(TrainDataUrls)
    app.config[
        'SQLALCHEMY_DATABASE_URI'] = f'mysql://{MYSQL_USER}:{MYSQL_PASSWORD}@{MYSQL_HOST}/{MYSQL_DB}'
    app.config['SQLALCHEMY_ENGINE_OPTIONS'] = {
        'pool_size': 20,  # 连接池大小（默认 5）
        'max_overflow': 5,  # 超过连接池大小后允许的额外连接数（默认 10）
        'pool_timeout': 30,  # 获取连接的超时时间，默认 30 秒
        'pool_recycle': 1800,  # 连接回收时间（秒），防止 MySQL 连接超时
        'connect_args': {
            'init_command': "SET time_zone='+08:00'"
        }
    }
    # 应该显示上海时间
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['SQLALCHEMY_ECHO'] = False
    app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=1)
    app.config['JWT_REFRESH_TOKEN_EXPIRES'] = timedelta(days=30)
    app.config['JWT_TOKEN_LOCATION'] = ['headers']
    app.config['JWT_HEADER_NAME'] = 'Authorization'
    app.config['JWT_HEADER_TYPE'] = 'Bearer'
    app.config['JSON_AS_ASCII'] = False  # 关键点
    db.init_app(app)

    return app


app = create_app()
if __name__ == '__main__':

    app.run(host='0.0.0.0', port=5007, debug=True, use_reloader=False)
