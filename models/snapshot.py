from datetime import datetime
from . import db
from .snapshot_samples import snapshot_samples
from . import shanghai_time

class Snapshot(db.Model):
    __tablename__ = 'snapshots'
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    snapshot_id = db.Column(db.String(255), unique=True, nullable=False)
    user_name = db.Column(db.String(255), nullable=False, index=True)
    user_id = db.Column(db.String(255), db.<PERSON>Key('user.user_id'), nullable=False, index=True)
    description = db.Column(db.String(255), unique=False, nullable=False)
    create_at = db.Column(db.DateTime(timezone=True), nullable=False, default=shanghai_time, index=True)
    create_record = db.Column(db.TEXT, unique=False, nullable=False)
    samples = db.relationship('Sample', 
                          secondary=snapshot_samples,
                          primaryjoin="Snapshot.snapshot_id == snapshot_samples.c.snapshot_id",
                          secondaryjoin="and_(Sample.sample_uuid == snapshot_samples.c.sample_uuid, "
                                      "Sample.version == snapshot_samples.c.sample_version)",
                          backref=db.backref('snapshot', lazy=True))

    def __repr__(self):
        return f'<Snapshot {self.snapshot_id}>'
