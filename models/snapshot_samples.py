# 创建一个关联表，管理 Snapshot 和 Sample 之间的多对多关系
from . import db
snapshot_samples = db.Table(
    'snapshot_samples',
    db.<PERSON><PERSON>n('snapshot_id', db.String(255), db.<PERSON>('snapshots.snapshot_id'), primary_key=True, index=True),
    db.<PERSON>('sample_uuid', db.String(255), db.<PERSON><PERSON>('samples.sample_uuid'), primary_key=True, index=True),
    db.<PERSON>('sample_version', db.Integer, primary_key=False, index=True),
    db.<PERSON>umn('sample_type', db.String(255), primary_key=False, index=True)
)