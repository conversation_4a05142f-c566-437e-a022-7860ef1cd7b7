from datetime import datetime
from . import db
from . import shanghai_time
class Sample(db.Model):
    __tablename__ = 'samples'
    id = db.Column(db.Integer, primary_key=True)
    sample_uuid = db.Column(db.String(255), unique=True, nullable=False)
    create_user = db.Column(db.String(255), nullable=False, index=True)
    total_datapoints = db.Column(db.Integer, nullable=False)
    type = db.Column(db.String(255), nullable=False, index=True)
    version = db.Column(db.Integer, index=True, nullable=False)
    create_at = db.Column(db.DateTime(timezone=True), nullable=False, default=shanghai_time, index=True)
    update_at = db.Column(db.DateTime(timezone=True), nullable=False, default=shanghai_time, onupdate=shanghai_time)
    # 多对多关系，使用 `snapshot_samples` 关联表
    def __repr__(self):
        return f'<Sample {self.sample_uuid}>'
