ARG BASE_IMAGE=registry.qiuzhi.tech/devops/ubuntu24:dataloop
  
FROM ${BASE_IMAGE}

WORKDIR /app

COPY . .

RUN pip install --ignore-installed -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple  --break-system-package


RUN pip install Flask-JWT-Extended==4.5.3 -i https://mirrors.aliyun.com/pypi/simple --break-system-package

EXPOSE 5001


RUN chmod 777 start_gunicorn.sh

CMD ["./start_gunicorn.sh"]