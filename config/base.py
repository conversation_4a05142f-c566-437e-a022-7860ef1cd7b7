"""
Configuration module for the Data Server application.
This module contains all the configuration settings for different components of the application.
Environment variables can be used to override these default settings.
"""

import os
from typing import Dict, Any


class BaseConfig:
    """
    Base configuration class containing all application settings.
    All configuration values can be overridden using environment variables.
    """

    # Application Settings
    DEBUG: bool = False
    TESTING: bool = False

    # Upload Settings
    UPLOAD_FOLDER: str = os.getenv('UPLOAD_FOLDER', 'uploads/')

    # Minio Object Storage Settings
    MINIO_CONFIG: Dict[str, Any] = {
        'server': os.getenv('MINIO_SERVER', 'localhost:9000'),
        'access_key': os.getenv('MINIO_ACCESS_KEY', ''),
        'secret_key': os.getenv('MINIO_SECRET_KEY', ''),
        'secure': os.getenv('MINIO_SECURE', 'False').lower() == 'true',
        'bucket_name': os.getenv('BUCKET_NAME', 'test')
    }

    # Redis Cache and Queue Settings
    REDIS_CONFIG: Dict[str, Any] = {
        'host': os.getenv('REDIS_HOST', 'localhost'),
        'port': int(os.getenv('REDIS_PORT', 6379)),
        'password': os.getenv('REDIS_PASSWORD', ''),
        'db': int(os.getenv('REDIS_DB', 0)),
        'task_queue': os.getenv('REDIS_TASK_QUEUE', 'task_queue')
    }

    # MySQL Database Settings
    MYSQL_CONFIG: Dict[str, Any] = {
        'host': os.getenv('MYSQL_HOST', 'localhost:3306'),
        'user': os.getenv('MYSQL_USER', 'root'),
        'password': os.getenv('MYSQL_PASSWORD', ''),
        'database': os.getenv('MYSQL_DB', 'airbot_engine_test')
    }

    # WebSocket Server Settings
    WEBSOCKET_SERVER_URL: str = os.getenv('WEBSOCKET_SERVER_URL', 'ws://localhost:8080')

    # Project Settings
    DEFAULT_PROJECT_ID: int = int(os.getenv('DEFAULT_PROJECT_ID', 1))

    @classmethod
    def get_config(cls) -> Dict[str, Any]:
        """
        Get all configuration settings as a dictionary.

        Returns:
            Dict[str, Any]: Dictionary containing all configuration settings
        """
        return {
            key: value for key, value in cls.__dict__.items()
            if not key.startswith('_') and not callable(value)
        }
