from .base import BaseConfig

class DevelopmentConfig(BaseConfig):
    DEBUG = False
    DEFAULT_PROJECT_ID = 109
    RECYCLE_PROJECT_ID = 6
    WEBSOCKET_SERVER_URL = "ws://***************:8765"
    # Production settings should be configured through environment variables
    # for better security and flexibility
    # Override with development-specific settings
    MINIO_CONFIG = {
        'server': '***************:9000',
        'access_key': 'xNYNu2DvIRBJPs9963FQ',
        'secret_key': 'ZqtIxZd0PMBhU32NV7mNSLbufVbyQ5ZxOMlbblLB',
        'secure': False,
        'bucket_name': 'airbot',
        'model_bucket_name': 'model'

    }

    REDIS_CONFIG = {
        'host': '***************',
        'port': 6389,
        'password': '123456',
        'db': 4,
        'task_queue': 'task_queue'
    }


    MYSQL_CONFIG = {
        'host': '***************:3310',
        'user': 'root',
        'password': '123456',
        'database': 'airbot_dataloop_dev',
        'user_database': 'airbot_dataloop_identify'
    }

    HARBOR_CONFIG = {
        'host': 'registry.qiuzhi.tech'
    }

    KUBERNETES_CONFIG = {
        'train_namespace': 'train',
        'inference_namespace': 'inference',
        'quantify_namespace': 'quantify',
        'deploy_namespace': 'deploy',
        'simulator_namespace': 'simulator'
    }
    DATALOOP = {
        'host': '***************',
        'username':'admin',
        'password':'123456'
    }
    MLFLOW_CONFIG = {
        'host': 'http://***************:30900',
        'AWS_ACCESS_KEY_ID':'xNYNu2DvIRBJPs9963FQ',
        'AWS_SECRET_ACCESS_KEY':'ZqtIxZd0PMBhU32NV7mNSLbufVbyQ5ZxOMlbblLB',
        'MLFLOW_S3_ENDPOINT_URL':'http://***************:9000'
    }
    MONGODB_CONFIG = {
        'host': '***************',
        'port': 27017,
        'username': 'admin',
        'password': '123456',
        'database': 'airbot_dataloop_tags',
        'sample_tags_collection':"sample_tags",
        'snapshot_tags_collection':"snapshot_tags",
        "annotation_info_collection":"annotation_info"
    }