import os
from .base import BaseConfig
from config.Development import DevelopmentConfig
from config.C2m import C2MConfig

class RedisConfigClass:
    def __init__(self, config_dict):
        self.REDIS_HOST = config_dict['host']
        self.REDIS_PORT = config_dict['port']
        self.REDIS_PASSWORD = config_dict['password']
        self.REDIS_DB = config_dict['db']
        self.REDIS_TASK_QUEUE = config_dict['task_queue']

class MinioConfigClass:
    def __init__(self, config_dict):
        self.MINIO_SERVER = config_dict['server']
        self.MINIO_ACCESS_KEY = config_dict['access_key']
        self.MINIO_SECRET_KEY = config_dict['secret_key']
        self.MINIO_SECURE = config_dict['secure']
        self.BUCKET_NAME = config_dict['bucket_name']

# Select configuration based on environment variable
env = os.getenv('FLASK_ENV', 'development')

if env == 'development':
    config = DevelopmentConfig()
    KUBERNETES_CONFIG_PATH = os.path.join(os.path.dirname(__file__), "kubeconfigTest.yaml")
else:
    config = C2MConfig()
    KUBERNETES_CONFIG_PATH = os.path.join(os.path.dirname(__file__), "kubeconfigQimeng.yaml")

# Export configuration instance
ANSIBLE_INVENTORY_PATH = os.path.join(os.path.dirname(__file__), "ansible/inventory.ini")
ANSIBLE_PLAYBOOK_PATH = os.path.join(os.path.dirname(__file__), "ansible/ansible-playbook.yml")
current_config = config

# Export common configuration items
UPLOAD_FOLDER = current_config.UPLOAD_FOLDER

# MySQL configuration
MYSQL_CONFIG = current_config.MYSQL_CONFIG
MYSQL_HOST = MYSQL_CONFIG['host']
MYSQL_USER = MYSQL_CONFIG['user']
MYSQL_PASSWORD = MYSQL_CONFIG['password']
MYSQL_DB = MYSQL_CONFIG['database']
MYSQL_USER_DB = MYSQL_CONFIG['user_database']


# Redis configuration
REDIS_CONFIG = current_config.REDIS_CONFIG
RedisConfig = RedisConfigClass(REDIS_CONFIG)
REDIS_DB = REDIS_CONFIG['db']

# MinIO configuration
MINIO_CONFIG = current_config.MINIO_CONFIG
MinioConfig = MinioConfigClass(MINIO_CONFIG)
BUCKET_NAME = MINIO_CONFIG['bucket_name']
MODEL_BUCKET_NAME = MINIO_CONFIG['model_bucket_name']


MONGODB_CONFIG = current_config.MONGODB_CONFIG
MONGODB_HOST = MONGODB_CONFIG['host']
MONGODB_PORT = MONGODB_CONFIG['port']
MONGODB_USER = MONGODB_CONFIG['username']
MONGODB_PASSWORD = MONGODB_CONFIG['password']
MONGODB_DATABASE = MONGODB_CONFIG['database']
MONGODB_SAMPLE_TAGS_COLLECTION = MONGODB_CONFIG['sample_tags_collection']
MONGODB_SNAPSHOT_TAGS_COLLECTION = MONGODB_CONFIG['snapshot_tags_collection']
MONGODB_ANNOTATION_INFO_COLLECTION = MONGODB_CONFIG['annotation_info_collection']
