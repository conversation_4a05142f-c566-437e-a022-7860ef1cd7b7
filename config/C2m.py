from .base import BaseConfig

class C2MConfig(BaseConfig):
    DEBUG = False

    DEFAULT_PROJECT_ID = 1
    RECYCLE_PROJECT_ID = 40
    WEBSOCKET_SERVER_URL = "ws://**********:8765"
    # Production settings should be configured through environment variables
    # for better security and flexibility
    # Override with development-specific settings
    MINIO_CONFIG = {
        'server': '**********:9000',
        'access_key': 'ri4hdeuYJ4M9a5VRVLtV',
        'secret_key': '60SYjfAnOr9P9dJdJ0dMNZLmnUzX6ArdLr1HuEzy',
        'secure': False,
        'bucket_name': 'dataloop',
        'model_bucket_name': 'model'
    }

    REDIS_CONFIG = {
        'host': '**********',
        'port': 6379,
        'password': '123456',
        'db': 6,
        'task_queue': 'task_queue'
    }

    MYSQL_CONFIG = {
        'host': '**********',
        'user': 'root',
        'password': '123456',
        'database': 'airbot_dataloop_qimeng',
        'user_database': 'airbot_dataloop_identify'
    }

    HARBOR_CONFIG = {
        'host': 'registry.qiuzhi.tech'
    }

    KUBERNETES_CONFIG = {
        'train_namespace': 'train',
        'inference_namespace': 'inference',
        'quantify_namespace': 'quantify',
        'deploy_namespace': 'deploy'
    }
    DATALOOP = {
        'host': '**********',
        'username':'admin',
        'password':'123456'
    }
    MLFLOW_CONFIG = {
        'host': 'http://**********:30900',
        'AWS_ACCESS_KEY_ID':'ri4hdeuYJ4M9a5VRVLtV',
        'AWS_SECRET_ACCESS_KEY':'60SYjfAnOr9P9dJdJ0dMNZLmnUzX6ArdLr1HuEzy',
        'MLFLOW_S3_ENDPOINT_URL':'http://**********:9000'
    }
    MONGODB_CONFIG = {
        'host': '**********',
        'port': 27017,
        'username': 'admin',
        'password': '123456',
        'database': 'airbot_dataloop_tags',
        'sample_tags_collection':"sample_tags",
        'snapshot_tags_collection':"snapshot_tags"
    }

