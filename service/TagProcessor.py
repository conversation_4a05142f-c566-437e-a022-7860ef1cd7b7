from typing import Dict, Any, Set, List, Union, Optional
from collections import defaultdict
from bson import ObjectId
from sqlalchemy.orm import aliased
from service.MongoDBClient import MongoDBConnection
from service.DocumentProcessor import DocumentProcessor
from models import db


class TagProcessor:
    """标签处理器"""

    def __init__(self):
        self.mongo_conn = MongoDBConnection()
        self.doc_processor = DocumentProcessor()

    def parse_document_id(self, doc_id: str) -> Union[ObjectId, str]:
        """解析文档ID"""
        try:
            return ObjectId(doc_id)
        except Exception:
            return doc_id

    def query_documents_by_ids(self, doc_ids: List[str]) -> List[Dict]:
        """根据文档ID列表查询文档"""
        if not doc_ids:
            return []

        try:
            with self.mongo_conn.get_connection() as (mongodb_sample_collection, mongodb_snapshot_collection):
                parsed_ids = [self.parse_document_id(doc_id) for doc_id in doc_ids]

                query = {"_id": {"$in": parsed_ids}}
                documents = list(mongodb_sample_collection.find(query))

                print(f"查询 {len(doc_ids)} 个文档ID，找到 {len(documents)} 个文档")

                # 检查未找到的文档
                found_ids = {str(doc["_id"]) for doc in documents}
                not_found = set(doc_ids) - found_ids
                if not_found:
                    print(f"未找到的文档ID: {list(not_found)}")

                return documents

        except Exception as e:
            print(f"查询文档时出错: {e}")
            return []

    def format_tags(self, unique_values: Dict[str, Set[str]]) -> Dict[str, List[str]]:
        """格式化标签数据"""
        formatted_tags = {}

        for key, values in unique_values.items():
            values_list = sorted(list(values))

            if len(values_list) == 1:
                formatted_tags[key] = values_list
            else:
                formatted_tags[key] = values_list

        return formatted_tags

    def get_one_sample_tags(self, sample_id: str) -> Optional[Dict[str, List[str]]]:
        """获取单个样本的标签"""
        try:
            document_ids = [sample_id]

            if not document_ids:
                print("没有找到任何样本UUID")
                return None

            documents = self.query_documents_by_ids(document_ids)

            if not documents:
                print("没有找到任何文档")
                return None

            unique_values = self.doc_processor.collect_one_unique_key_values(documents)

            formatted_tags = self.format_tags(unique_values)
            return formatted_tags

        except Exception as e:
            print(f"获取样本标签时出错: {e}")
            return {}

    def get_batch_sample_tags(self, sample_ids: List[str]) -> Dict[str, Dict[str, List[str]]]:
        """
        批量获取多个样本的标签数据

        Args:
            sample_ids: 样本ID列表

        Returns:
            Dict[str, Dict[str, List[str]]]: 样本ID到标签字典的映射
        """
        if not sample_ids:
            return {}

        try:
            with self.mongo_conn.get_connection() as (mongodb_sample_collection, mongodb_snapshot_collection):
                # 解析文档ID
                parsed_ids = [self.parse_document_id(doc_id) for doc_id in sample_ids]

                # 批量查询所有样本的文档
                query = {"_id": {"$in": parsed_ids}}
                documents = list(mongodb_sample_collection.find(query))

                print(f"批量查询 {len(sample_ids)} 个样本ID，找到 {len(documents)} 个文档")

                # 按文档ID分组文档
                docs_by_id = {}
                for doc in documents:
                    doc_id = str(doc["_id"])
                    docs_by_id[doc_id] = doc

                # 为每个样本处理标签
                result = {}
                for sample_id in sample_ids:
                    if sample_id in docs_by_id:
                        # 找到对应文档，处理标签
                        doc = docs_by_id[sample_id]
                        unique_values = self.doc_processor.collect_one_unique_key_values([doc])
                        formatted_tags = self.format_tags(unique_values)
                        result[sample_id] = formatted_tags
                    else:
                        # 未找到文档，返回空字典
                        result[sample_id] = {}

                # 记录未找到的文档
                found_ids = set(docs_by_id.keys())
                not_found = set(sample_ids) - found_ids
                if not_found:
                    print(f"批量查询中未找到的文档ID: {list(not_found)}")

                return result

        except Exception as e:
            print(f"批量获取样本标签时出错: {e}")
            # 返回空字典映射，确保每个样本ID都有对应的空字典
            return {sample_id: {} for sample_id in sample_ids}

    def get_batch_snapshot_tags(self, snapshot_ids: List[str]) -> Dict[str, Dict[str, List[str]]]:
        """
        批量获取多个快照的标签数据

        Args:
            snapshot_ids: 快照ID列表

        Returns:
            Dict[str, Dict[str, List[str]]]: 快照ID到标签字典的映射
        """
        if not snapshot_ids:
            return {}

        try:
            with self.mongo_conn.get_connection() as (mongodb_sample_collection, mongodb_snapshot_collection):
                # 解析文档ID
                parsed_ids = [self.parse_document_id(doc_id) for doc_id in snapshot_ids]

                # 批量查询所有快照的文档
                query = {"_id": {"$in": parsed_ids}}
                documents = list(mongodb_snapshot_collection.find(query))

                print(f"批量查询 {len(snapshot_ids)} 个快照ID，找到 {len(documents)} 个文档")

                # 按文档ID分组文档
                docs_by_id = {}
                for doc in documents:
                    doc_id = str(doc["_id"])
                    docs_by_id[doc_id] = doc

                # 为每个快照处理标签
                result = {}
                for snapshot_id in snapshot_ids:
                    if snapshot_id in docs_by_id:
                        # 找到对应文档，处理标签
                        doc = docs_by_id[snapshot_id]
                        unique_values = self.doc_processor.collect_one_unique_key_values([doc])
                        formatted_tags = self.format_tags(unique_values)
                        result[snapshot_id] = formatted_tags
                    else:
                        # 未找到文档，返回空字典
                        result[snapshot_id] = {}

                # 记录未找到的文档
                found_ids = set(docs_by_id.keys())
                not_found = set(snapshot_ids) - found_ids
                if not_found:
                    print(f"批量查询中未找到的快照ID: {list(not_found)}")

                return result

        except Exception as e:
            print(f"批量获取快照标签时出错: {e}")
            # 返回空字典映射，确保每个快照ID都有对应的空字典
            return {snapshot_id: {} for snapshot_id in snapshot_ids}

    def get_all_tags(self) -> Optional[Dict[str, List[str]]]:
        """获取所有标签"""
        try:
            documents = []

            with self.mongo_conn.get_connection() as (mongodb_sample_collection, mongodb_snapshot_collection):
                documents = list(mongodb_sample_collection.find())

            # 查询MongoDB文档
            if not documents:
                print("没有找到任何文档")
                return None

            unique_values = self.doc_processor.collect_unique_key_values(documents)

            formatted_tags = self.format_tags(unique_values)

            result = self.doc_processor.merge_array_keys(formatted_tags)

            return result

        except Exception as e:
            print(f"获取样本标签时出错: {e}")
            return {}

    def get_sample_tags(self, project_ids: List[str]) -> Optional[Dict[str, List[str]]]:
        """获取样本标签"""
        try:
            from models.sample import Sample

            sa = aliased(Sample)
            results = (
                db.session.query(sa.sample_uuid)
                .filter(sa.project_id.in_(project_ids))
                .distinct()
                .all()
            )

            document_ids = [result.sample_uuid for result in results]

            if not document_ids:
                print("没有找到任何样本UUID")
                return None

            # 查询MongoDB文档
            documents = self.query_documents_by_ids(document_ids)

            if not documents:
                print("没有找到任何文档")
                return None

            # 处理文档数据
            print("正在分析键值对...")
            unique_values = self.doc_processor.collect_unique_key_values(documents)

            # 格式化标签
            formatted_tags = self.format_tags(unique_values)

            # 处理数组键
            result = self.doc_processor.merge_array_keys(formatted_tags)

            print(f"处理完成，共找到 {len(result)} 个标签")
            return result

        except Exception as e:
            print(f"获取样本标签时出错: {e}")
            return {}

    def get_snapshot_tags(self) -> Dict[str, List[str]]:
        """获取快照标签"""
        try:
            documents = []
            with self.mongo_conn.get_connection() as (mongodb_sample_collection, mongodb_snapshot_collection):
                documents = list(mongodb_snapshot_collection.find())

            # 查询MongoDB文档
            if not documents:
                print("没有找到任何文档")
                return None

            # 处理文档数据
            print("正在分析键值对...")
            unique_values = self.doc_processor.collect_unique_key_values(documents)

            # 格式化标签
            formatted_tags = self.format_tags(unique_values)

            # 处理数组键
            result = self.doc_processor.merge_array_keys(formatted_tags)

            print(f"处理完成，共找到 {len(result)} 个标签")
            return result

        except Exception as e:
            print(f"获取快照标签时出错: {e}")
            return {}

    def get_one_snapshot_tags(self, snapshot_id: str) -> Optional[Dict[str, List[str]]]:
        """获取单个快照的标签"""
        try:
            document_ids = [snapshot_id]

            if not document_ids:
                print("没有找到任何快照UUID")
                return None

            with self.mongo_conn.get_connection() as (mongodb_sample_collection, mongodb_snapshot_collection):
                query = {"_id": {"$in": document_ids}}
                documents = list(mongodb_snapshot_collection.find(query))

            if not documents:
                print("没有找到任何文档")
                return None

            unique_values = self.doc_processor.collect_one_unique_key_values(documents)
            # 格式化标签
            formatted_tags = self.format_tags(unique_values)
            return formatted_tags

        except Exception as e:
            print(f"获取快照标签时出错: {e}")
            return {}