from minio import Minio
from urllib3.poolmanager import <PERSON>Manager
from config import MinioConfig



MINIO_ENDPOINT = MinioConfig.MINIO_SERVER
MINIO_ACCESS_KEY = MinioConfig.MINIO_ACCESS_KEY
MINIO_SECRET_KEY = MinioConfig.MINIO_SECRET_KEY
MINIO_SECURE = MinioConfig.MINIO_SECURE



class MinioClient:
    def __init__(self):

        self.minio_config = MinioConfig()



    @staticmethod
    def create_optimized_minio_client():
        pool_manager = PoolManager(
            num_pools=10,
            maxsize=20,
            block=True
        )
        print(MINIO_ENDPOINT)
        print(MINIO_ACCESS_KEY)
        print(MINIO_SECRET_KEY)
        print(pool_manager)

        client = Minio(
            endpoint=MINIO_ENDPOINT,
            access_key=MINIO_ACCESS_KEY,
            secret_key=MINIO_SECRET_KEY,
            secure=MINIO_SECURE,
            http_client=pool_manager
        )
        return client