import json
import re
from typing import Dict, Any, Set, List, Union, Optional
from collections import defaultdict
from contextlib import contextmanager

class DocumentProcessor:
    """文档处理器"""

    @staticmethod
    def flatten_json(data: Dict[str, Any], parent_key: str = '', sep: str = '.') -> Dict[str, Any]:
        """递归扁平化JSON对象"""
        items = []

        for key, value in data.items():
            # 跳过MongoDB的_id字段
            if key == '_id':
                continue

            new_key = f"{parent_key}{sep}{key}" if parent_key else key

            if isinstance(value, dict):
                items.extend(DocumentProcessor.flatten_json(value, new_key, sep=sep).items())
            elif isinstance(value, list):
                for i, item in enumerate(value):
                    if isinstance(item, dict):
                        items.extend(DocumentProcessor.flatten_json(item, f"{new_key}[{i}]", sep=sep).items())
                    else:
                        items.append((f"{new_key}[{i}]", item))
            else:
                items.append((new_key, value))

        return dict(items)

    @staticmethod
    def flatten_one_json(data: Dict[str, Any], parent_key: str = '', sep: str = '.') -> Dict[str, Any]:
        """递归扁平化JSON对象"""
        items = []

        for key, value in data.items():
            # 跳过MongoDB的_id字段
            if key == '_id':
                continue

            new_key = f"{parent_key}{sep}{key}" if parent_key else key

            if isinstance(value, dict):
                items.extend(DocumentProcessor.flatten_one_json(value, new_key, sep=sep).items())
            elif isinstance(value, list):
                for i, item in enumerate(value):
                    if isinstance(item, dict):
                        items.extend(DocumentProcessor.flatten_one_json(item, f"{new_key}.{i}", sep=sep).items())
                    else:
                        items.append((f"{new_key}.{i}", item))
            else:
                items.append((new_key, value))

        return dict(items)




    @staticmethod
    def collect_unique_key_values(documents: List[Dict]) -> Dict[str, Set[str]]:
        """从所有文档中收集唯一的键值对"""
        unique_values = defaultdict(set)

        for doc in documents:
            flattened = DocumentProcessor.flatten_json(doc)
            for key, value in flattened.items():
                unique_values[key].add(str(value))

        return dict(unique_values)

    @staticmethod
    def collect_one_unique_key_values(documents: List[Dict]) -> Dict[str, Set[str]]:
        """从所有文档中收集唯一的键值对"""
        unique_values = defaultdict(set)

        for doc in documents:
            flattened = DocumentProcessor.flatten_one_json(doc)
            for key, value in flattened.items():
                unique_values[key].add(str(value))

        return dict(unique_values)


    @staticmethod
    def merge_array_keys(input_dict: Dict[str, Any]) -> Dict[str, List]:
        """合并数组类型的键为列表"""
        array_keys = defaultdict(dict)
        result = {}

        for key, value in input_dict.items():
            array_match = re.search(r'^(.+)\[(\d+)\](.*)$', key)

            if array_match:
                base_part = array_match.group(1)
                index = int(array_match.group(2))
                suffix = array_match.group(3)

                base_key = f"{base_part}{suffix}" if suffix else base_part
                array_keys[base_key][index] = value[0]
            else:
                result[key] = value
        # 将数组键转换为列表
        for base_key, indexed_values in array_keys.items():
            if indexed_values:
                max_index = max(indexed_values.keys())
                value_list = [''] * (max_index + 1)

                for index, value in indexed_values.items():
                    value_list[index] = value

                result[base_key] = value_list

        return result