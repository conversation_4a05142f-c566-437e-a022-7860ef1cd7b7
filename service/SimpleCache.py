import threading
import time
class SimpleCache:
    def __init__(self, ttl=300):  # 5分钟过期
        self.cache = {}
        self.timestamps = {}
        self.ttl = ttl
        self.lock = threading.RLock()

    def get(self, key):
        with self.lock:
            if key in self.cache:
                if time.time() - self.timestamps[key] < self.ttl:
                    return self.cache[key]
                else:
                    del self.cache[key]
                    del self.timestamps[key]
            return None

    def set(self, key, value):
        with self.lock:
            self.cache[key] = value
            self.timestamps[key] = time.time()

    def clear_expired(self):
        with self.lock:
            current_time = time.time()
            expired_keys = [
                key for key, timestamp in self.timestamps.items()
                if current_time - timestamp >= self.ttl
            ]
            for key in expired_keys:
                del self.cache[key]
                del self.timestamps[key]