from contextlib import contextmanager
from pymongo import Mongo<PERSON>lient
from config import (MON<PERSON><PERSON>B_HOST, MONGODB_PORT, MONGODB_USER, MONGODB_PASSWORD,
                    MONGODB_DATABASE , MONGODB_SNAPSHOT_TAGS_COLLECTION,
                    <PERSON>ON<PERSON><PERSON><PERSON>_SAMPLE_TAGS_COLLECTION,MONGODB_ANNOTATION_INFO_COLLECTION)

class MongoDBConnection:
    """MongoDB连接管理器"""

    def __init__(self):
        self.uri = f"mongodb://{MONGODB_USER}:{MONGODB_PASSWORD}@{MONGODB_HOST}:{MONGODB_PORT}/{MONGODB_DATABASE}?authSource={MONGODB_USER}"
        self.client = None
        self.db = None

    @contextmanager
    def get_connection(self):
        self.client = MongoClient(self.uri)
        self.db = self.client[MONGODB_DATABASE]

        sample_tags = self.db[MONG<PERSON>B_SAMPLE_TAGS_COLLECTION]
        snapshot_tags = self.db[MONGODB_SNAPSHOT_TAGS_COLLECTION]
        annotation_info = self.db[MONGODB_ANNOTATION_INFO_COLLECTION]

        yield sample_tags, snapshot_tags

