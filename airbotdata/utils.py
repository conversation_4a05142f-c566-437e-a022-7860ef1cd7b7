import fractions
from io import BytesIO

import av
import cv2
import numpy as np


def encode_h264(data: list[dict]) -> bytes:
    outbuf = BytesIO()
    container = av.open(outbuf, 'w', format='mp4', options={'preset': 'fast'})
    stream = container.add_stream('h264', options={'preset': 'fast'})
    stream.width = data[0]['data'].shape[1]
    stream.height = data[0]['data'].shape[0]
    stream.pix_fmt = 'yuv420p'
    stream.time_base = fractions.Fraction(1, 1000)
    start_time = data[0]['t']
    for frame in data:
        video_frame = av.VideoFrame.from_ndarray(frame['data'], format='bgr24')
        video_frame.pts = frame['t'] - start_time
        video_frame.time_base = fractions.Fraction(1, 1000)
        for packet in stream.encode(video_frame):
            container.mux(packet)
    for packet in stream.encode():
        container.mux(packet)
    container.close()
    return outbuf.getvalue()



def decode_h264(h264_bytes: bytes) -> list[dict[str, np.ndarray | int]]:
    inbuf = BytesIO(h264_bytes)
    container = av.open(inbuf)
    ret = [{
        "t": int(frame.pts * frame.time_base * 1e3),
        "data": frame.to_ndarray(format="bgr24")
    } for frame in container.decode(video=0)]
    assert len(ret) > 0, "No frames found in h264"
    return ret


def decode_jpeg(jpeg_bytes: bytes) -> np.ndarray:
    return cv2.imdecode(np.frombuffer(jpeg_bytes, np.uint8), cv2.IMREAD_COLOR)