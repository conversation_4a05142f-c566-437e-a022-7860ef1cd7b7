"""
Defines the basic structure and deserializer
"""
import uuid
import numpy as np
from .schema import And, Or, Regex, Schema, Use

from .utils import decode_h264, decode_jpeg


class SCHEMAV120:
    """
    Schema definition version 1.2.1
    """
    VERSION = "1.2.1"

    @staticmethod
    def valid_topic_type(t: str):
        return t in ["image", "jointstate"]

    @staticmethod
    def valid_encoding(t: str):
        return t in ["JPEG", "H264"]

    @staticmethod
    def valid_uuid(test_str: str) -> bool:
        try:
            uuid.UUID(test_str)
            return True
        except ValueError:
            return False

    DPT_POSE_META = Schema({
        "description": str,
        "type": "pose",
    })

    DPT_POSE = Schema({
        "t": int,
        "data": {
            "t": [float],
            "r": [float],
        },
    })

    # "t" is timestamp in milliseconds
    # "data" is a bytearray of jpeg (or other codec, as specified in the meta info) encoded frame
    DPT_IMAGE_FRAME = Schema({
        "t":
            int,
        "data":
            Or(
                np.ndarray,
                Use(decode_jpeg,
                    error="Fail to decode JPEG in image data point"))
    })

    DPT_IMAGE_META = Schema({
        "description": str,
        "type": "image",
        "width": int,
        "height": int,
        "encoding": str,
        "distortion_model": Or(str, None),
        "distortion_params": Or(And([float], lambda x: len(x) == 5), None),
        "intrinsics": Or(And([float], lambda x: len(x) == 4), None),
        "fov": float,
        "start_time": int,
    })

    # "data" is a bytearray of h264 (or other codec, as specified in the meta info) encoded video frames
    # Timestamps are in milliseconds; already integrated in the bytearray
    DPT_IMAGE_VIDEO = Schema(
        Or(
            [{
                "t": int,
                "data": np.ndarray,
            }],
            Use(decode_h264,
    # error="Fail to decode H264 in image data points as video stream"
               ),
        ))
    DPT_JOINTSTATES_META = Schema({
        "description": str,
        "type": "jointstate",
        "sn": str,
        "firmware_version": Regex(r'^\d+\.\d+\.\d+$'),
    })

    @staticmethod
    def valid_jointstate_data(data: dict) -> bool:
        # Check if all values are None
        if all(value is None for value in data['data'].values()):
            return False

        # Filter out None values
        non_none_values = [
            value for value in data['data'].values() if value is not None
        ]

        # Check if the lengths of non-None values are the same
        if len({len(value) for value in non_none_values}) != 1:
            return False

        return True

    DPT_JOINTSTATES = Schema(
        And(
            {
                "t": int,
                "data": {
                    "pos": Or([float], None),
                    "vel": Or([float], None),
                    "eff": Or([float], None),
                },
            }, Use(valid_jointstate_data)))






    SAMPLE_NonSequential = Schema(
        And(
            {
                "id": And(str, valid_uuid),
                "timestamp": int,
                "type": str,
                "version": int,
                "tags": dict,
                "metadata": {
                    "version": VERSION,
                    "operator": Or(str, None),
                    "task": str,
                    "station_id": And(str, valid_uuid),
                    "driver_version": Regex(r'^\d+\.\d+\.\d+$'),
                    "topics": {
                        str:
                            Or(DPT_IMAGE_META, DPT_JOINTSTATES_META,
                               DPT_POSE_META)
                    },
                },
                "data": {
                    str:
                        Or(
                            DPT_IMAGE_VIDEO, list[Or(
                                DPT_JOINTSTATES,
                                DPT_POSE,
                                DPT_IMAGE_FRAME,
                            )]),
                }
            },
            lambda x: set(x['metadata']['topics'].keys()) == set(x['data'].keys(
            ))))
    SAMPLE_Video = Schema(
        And(
            {
                "id": And(str, valid_uuid),
                "timestamp": int,
                "type": str,
                "version": int,
                "tags": dict,
                "metadata": {
                    "version": VERSION,
                    "operator": Or(str, None),
                    "task": str,
                    "station_id": And(str, valid_uuid),
                    "driver_version": Regex(r'^\d+\.\d+\.\d+$'),
                    "topics": {
                        str:
                            Or(DPT_IMAGE_META, DPT_JOINTSTATES_META,
                               DPT_POSE_META)
                    },
                },
                "data": {
                    str:
                        Or(
                            DPT_IMAGE_VIDEO, list[Or(
                                DPT_JOINTSTATES,
                                DPT_POSE,
                                DPT_IMAGE_FRAME,
                            )]),
                }
            },
            lambda x: set(x['metadata']['topics'].keys()) == set(x['data'].keys(
            ))))

    SAMPLE_Sequential = Schema(
        And(
            {
                "id": And(str, valid_uuid),
                "timestamp": int,
                "type": str,
                "version": int,
                "tags": dict,
                "metadata": {
                    "version": VERSION,
                    "operator": Or(str, None),
                    "task": str,
                    "station_id": And(str, valid_uuid),
                    "driver_version": Regex(r'^\d+\.\d+\.\d+$'),
                    "topics": {
                        str:
                            Or(DPT_IMAGE_META, DPT_JOINTSTATES_META,
                               DPT_POSE_META)
                    },
                },
                "data": {
                    str:
                        Or(
                            DPT_IMAGE_VIDEO, list[Or(
                                DPT_JOINTSTATES,
                                DPT_POSE,
                                DPT_IMAGE_FRAME,
                            )]),
                }
            },
            lambda x: set(x['metadata']['topics'].keys()) == set(x['data'].keys(
            ))))


SCHEMA = SCHEMAV120


def validate(sample: dict) -> dict:
    type = sample.get("type","")
    if type == "NonSequential":
        return SCHEMA.SAMPLE_NonSequential.validate(sample)
    elif type == "video":
        return SCHEMA.SAMPLE_Video.validate(sample)
    else:
        return SCHEMA.SAMPLE_Sequential.validate(sample)
