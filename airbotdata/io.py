import json
import logging
import time
import uuid
from copy import deepcopy
from os import makedirs
from pathlib import Path
import bson
import cv2
from .schemas import validate
from .utils import encode_h264
import base64


def load_bson(bson_path: Path) -> dict:
    with open(bson_path, "rb") as f:
        data = bson.decode(f.read())
    return validate(data)

def load_minio_bson(bson_data) -> dict:
    data = bson.decode(bson_data)
    return validate(data)


def save_bson(sample: dict, bson_path: Path):
    validate(sample)
    sample = deepcopy(sample)
    if sample["type"] == "Sequential" or sample["type"] == "video" or sample["type"] == "NonSequential":
        for topic, topic_data in sample['data'].items():
            if sample['metadata']['topics'][topic]['type'] == 'image':
                sample['data'][topic] = encode_h264(topic_data)
    makedirs(bson_path.parent, exist_ok=True)
    with open(bson_path, "wb") as f:
        f.write(bson.encode(sample))
def save_minio_bson(sample: dict):
    validate(sample)
    sample = deepcopy(sample)
    if sample["type"] == "Sequential" or sample["type"] == "video" or sample["type"] == "NonSequential":
        for topic, topic_data in sample['data'].items():
            if sample['metadata']['topics'][topic]['type'] == 'image':
                sample['data'][topic] = encode_h264(topic_data)
    return sample