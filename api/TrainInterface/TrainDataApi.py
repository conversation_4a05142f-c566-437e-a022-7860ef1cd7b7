from concurrent.futures import Thr<PERSON><PERSON>oolExecutor
from service.SimpleCache import SimpleC<PERSON>
from flask import Blueprint, jsonify, request, Response
from airbotdata.io import load_minio_bson
import asyncio
from minio.error import S3Error
from functools import wraps
from typing import Tuple,Dict,Any
from models.sample import Sample
import base64
from config import BUCKET_NAME
from service.MinioClient import MinioClient
import threading
import time
from flask_cors import CORS
import logging
logger = logging.getLogger(__name__)

TrainDataUrls = Blueprint('TrainDataUrls', __name__)
CORS(TrainDataUrls)
# 全局线程池
thread_pool = ThreadPoolExecutor(max_workers=20)
minio_client = MinioClient.create_optimized_minio_client()
# 缓存实例
sample_cache = SimpleCache(ttl=300)
bson_cache = SimpleCache(ttl=600)

# 2. 异步装饰器
def async_route(f):
    @wraps(f)
    def wrapper(*args, **kwargs):
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(f(*args, **kwargs))
        finally:
            loop.close()

    return wrapper


# 3. 优化后的路由
@TrainDataUrls.route('/api/snapshots/get_train_data', methods=['POST'])
def get_train_data() -> Tuple[Response, int]:
    start_time = time.time()  # 记录开始时间
    try:
        data = request.get_json()
        required_fields = ['sample_id']
        for field in required_fields:
            if not data or field not in data:
                return jsonify({
                    "error": f"{field} is required",
                    "code": 400,
                    "message": "Missing required field"
                }), 400

        sample_id = data["sample_id"]

        # 使用缓存获取sample信息
        sample = get_sample_cached(sample_id)
        if not sample:
            return jsonify({
                "error": "Sample not found",
                "code": 404,
                "message": "Sample not found"
            }), 404

        if sample.type == "NonSequential":
            # 使用线程池异步处理
            future = thread_pool.submit(get_minio_train_noseq_data_optimized, sample_id)
            sample_data = future.result(timeout=30)  # 30秒超时
            return jsonify({"code": 200, "data": sample_data, "message": "success"}), 200

        elif sample.type == "Sequential":
            supported_formats = {
                "mcap": "application/x-mcap",
                "bson": "application/bson"
            }
            return jsonify({"code": 200, "data": {}, "message": "success"}), 200
        else:
            return jsonify({
                "error": "Invalid sample type",
                "code": 400,
                "message": "Invalid sample type"
            }), 400

    except Exception as e:
        logger.exception("get_train_data failed")
        return jsonify({
            "error": "Internal server error",
            "code": 500,
            "message": "return image failed"
        }), 500
    finally:
        elapsed = round((time.time() - start_time) * 1000, 2)  # 毫秒
        print(f"[get_train_data] sample_id={data.get('sample_id') if data else None} "
                    f"elapsed_time={elapsed}ms")


# 4. 缓存的Sample查询
def get_sample_cached(sample_id: str):
    cache_key = f"sample_{sample_id}"
    sample = sample_cache.get(cache_key)

    if sample is None:
        sample = Sample.query.filter_by(sample_uuid=sample_id).first()
        if sample:
            sample_cache.set(cache_key, sample)

    return sample


# 5. 优化的数据获取函数
def get_minio_train_noseq_data_optimized(sample_id: str) -> Dict[str, Any]:
    # 检查完整数据缓存
    full_cache_key = f"full_data_{sample_id}"
    cached_data = bson_cache.get(full_cache_key)
    if cached_data:
        return cached_data

    sample = get_sample_cached(sample_id)
    if not sample:
        raise ValueError("Sample not found")

    # 并行获取tags和bson数据
    with ThreadPoolExecutor(max_workers=4) as executor:
        # 异步获取tags
        tags_future = executor.submit(get_tags_cached, sample_id)
        # 异步获取bson数据
        bson_future = executor.submit(get_bson_data_cached, sample_id)

        # 等待结果
        tags_dict = tags_future.result()
        sample_data = bson_future.result()

    result = {key: value[0] for key, value in tags_dict.items()}

    metadata = sample_data.get('metadata', {})
    image_topics = {
        topic for topic, attrs in metadata.get("topics", {}).items()
        if attrs.get("type") == "image"
    }

    # 移除cover数据
    if "cover" in sample_data.get("data", {}):
        del sample_data["data"]["cover"]
        del sample_data["metadata"]["topics"]["cover"]

    # 并行处理图片
    if image_topics:
        process_images_parallel(sample, sample_data, image_topics)

    sample_data["tags"] = result
    sample_data["version"] = sample.version

    # 缓存完整结果
    bson_cache.set(full_cache_key, sample_data)

    return sample_data


# 6. 缓存的tags获取
def get_tags_cached(sample_id: str):
    cache_key = f"tags_{sample_id}"
    tags_dict = sample_cache.get(cache_key)

    if tags_dict is None:
        from service.TagProcessor import TagProcessor
        tagprocessor = TagProcessor()
        tags_dict = tagprocessor.get_one_sample_tags(sample_id=sample_id)
        sample_cache.set(cache_key, tags_dict)

    return tags_dict


# 7. 缓存的BSON数据获取
def get_bson_data_cached(sample_id: str):
    cache_key = f"bson_{sample_id}"
    sample_data = bson_cache.get(cache_key)

    if sample_data is None:
        sample = get_sample_cached(sample_id)
        print(f"{sample.sample_uuid}/{sample.sample_uuid}.bson")
        response = minio_client.get_object(BUCKET_NAME, f"{sample.sample_uuid}/{sample.sample_uuid}.bson")
        bson_data = response.read()
        sample_data = load_minio_bson(bson_data)
        bson_cache.set(cache_key, sample_data)

    return sample_data


# 8. 并行图片处理
def process_images_parallel(sample, sample_data, image_topics):
    """并行处理图片数据"""

    def process_single_image(topic, sample_topic_data):
        """处理单个图片"""
        if "data" not in sample_topic_data:
            return

        import posixpath

        for ext in ['.jpg', '.png']:
            image_path = posixpath.join(sample.sample_uuid, str(topic), f"{sample_topic_data['t']}{ext}")

            # 检查图片缓存
            img_cache_key = f"img_{image_path}"
            image_base64 = sample_cache.get(img_cache_key)

            if image_base64:
                sample_topic_data["data"] = image_base64
                return

            try:
                image_bytes = minio_client.get_object(
                    bucket_name=BUCKET_NAME,
                    object_name=image_path
                )
                image_base64 = base64.b64encode(image_bytes.read()).decode('utf-8')
                sample_topic_data["data"] = image_base64

                # 缓存图片数据
                sample_cache.set(img_cache_key, image_base64)
                return

            except S3Error as e:
                if e.code == "NoSuchKey":
                    continue
                else:
                    raise


    tasks = []
    for topic in image_topics:
        if topic in sample_data["data"]:
            topic_str = str(topic)
            for sample_topic_data in sample_data["data"][topic_str]:
                tasks.append((topic, sample_topic_data))


    with ThreadPoolExecutor(max_workers=8) as executor:
        futures = [executor.submit(process_single_image, topic, data) for topic, data in tasks]

        for future in futures:
            future.result()


# 9. 定期清理缓存的后台任务
def start_cache_cleaner():
    """启动缓存清理线程"""

    def clean_cache():
        while True:
            time.sleep(300)
            sample_cache.clear_expired()
            bson_cache.clear_expired()

    cleaner_thread = threading.Thread(target=clean_cache, daemon=True)
    cleaner_thread.start()
